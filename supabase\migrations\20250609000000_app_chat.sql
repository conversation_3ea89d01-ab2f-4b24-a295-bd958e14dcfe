-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_chat CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_chat
;

GRANT USAGE ON SCHEMA app_chat TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_chat TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_chat TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_chat TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_chat
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_chat
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_chat
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section FUNCTIONS
-- anchor is_member
CREATE OR REPLACE FUNCTION app_chat.is_member (
  v_conversation_id UUID,
  v_user_id UUID
) RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT
      1
    FROM
      app_chat.member
    WHERE
      conversation_id = v_conversation_id
      AND user_id = v_user_id
  );
END;
$$
;

-- anchor is_conversation_blocked
CREATE OR REPLACE FUNCTION app_chat.is_conversation_blocked (v_conversation_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT
      1
    FROM
      app_chat.conversation
    WHERE
      id = v_conversation_id
      AND is_blocked
  );
END;
$$
;

-- anchor start_conversation
CREATE OR REPLACE FUNCTION app_chat.start_conversation (v_member_ids UUID[]) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_conversation_id UUID;
  v_existing_conversation_id UUID;
BEGIN
  IF NOT app_access.has_capability('chat.start_conversation') THEN
    RAISE EXCEPTION 'User does not have the chat.start_conversation capability.';
  END IF;

  -- Prevent starting a conversation if users have blocked each other
  IF ARRAY_LENGTH(v_member_ids, 1) = 2 THEN
    IF EXISTS (
      SELECT 1
      FROM app_account.user_block ub
      WHERE
        (ub.blocker_id = v_member_ids[1] AND ub.blocked_id = v_member_ids[2])
        OR (ub.blocker_id = v_member_ids[2] AND ub.blocked_id = v_member_ids[1])
    ) THEN
      RAISE EXCEPTION 'Cannot start a conversation with a user who has blocked you or whom you have blocked.';
    END IF;
  END IF;

  -- Ensure there are at least two members to start a conversation
  IF ARRAY_LENGTH(v_member_ids, 1) < 2 THEN
    RAISE EXCEPTION 'A conversation must have at least two members.';
  END IF;
  -- TODO: Remove this block when group chat is implemented
  IF ARRAY_LENGTH(v_member_ids, 1) > 2 THEN
    RAISE EXCEPTION 'Group conversations are not supported yet.';
  END IF;
  -- Check if at least one member is a provider
  IF NOT EXISTS (
    SELECT 1
    FROM app_access.user_role ur
    JOIN app_access.role r ON ur.role_id = r.id
    WHERE ur.user_id = ANY(v_member_ids) AND r.name = 'provider'
  ) THEN
    RAISE EXCEPTION 'At least one member must be a provider to start a conversation.';
  END IF;

  -- Sort the input member IDs for consistent comparison
  SELECT ARRAY_AGG(id ORDER BY id) INTO v_member_ids FROM UNNEST(v_member_ids) AS id;

  -- Check if a conversation with these exact members already exists
  SELECT
    c.id INTO v_existing_conversation_id
  FROM
    app_chat.conversation c
  JOIN
    (
      SELECT
        conversation_id,
        ARRAY_AGG(user_id ORDER BY user_id) AS members_array,
        COUNT(user_id) AS member_count
      FROM
        app_chat.member
      GROUP BY
        conversation_id
    ) AS cm ON c.id = cm.conversation_id
  WHERE
    cm.member_count = ARRAY_LENGTH(v_member_ids, 1)
    AND cm.members_array = v_member_ids
  LIMIT 1;

  IF v_existing_conversation_id IS NOT NULL THEN
    RETURN v_existing_conversation_id;
  ELSE
    -- Create a new conversation
    INSERT INTO app_chat.conversation (is_group) VALUES (ARRAY_LENGTH(v_member_ids, 1) > 2) RETURNING id INTO v_conversation_id;

    -- Add members to the new conversation
    INSERT INTO app_chat.member (conversation_id, user_id)
    SELECT v_conversation_id, UNNEST(v_member_ids);

    RETURN v_conversation_id;
  END IF;
END;
$$
;

-- !section
-- section TABLES
-- anchor conversation
CREATE TABLE app_chat.conversation (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  is_group BOOLEAN NOT NULL DEFAULT FALSE,
  is_blocked BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor member
CREATE TABLE app_chat.member (
  conversation_id UUID NOT NULL REFERENCES app_chat.conversation (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (conversation_id, user_id)
)
;

-- anchor message
CREATE TABLE app_chat.message (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  conversation_id UUID NOT NULL REFERENCES app_chat.conversation (id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
)
;

-- anchor deleted_message
CREATE TABLE app_chat.deleted_message (
  LIKE app_chat.message INCLUDING ALL
)
;

-- anchor conversation_preference
CREATE TABLE app_chat.conversation_preference (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  conversation_id UUID NOT NULL REFERENCES app_chat.conversation (id) ON DELETE CASCADE,
  muted BOOLEAN NOT NULL DEFAULT FALSE,
  muted_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor message_read
CREATE TABLE app_chat.message_read (
  message_id UUID NOT NULL REFERENCES app_chat.message (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (message_id, user_id)
)
;

-- anchor log
CREATE TABLE app_chat.log (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  conversation_id UUID NOT NULL REFERENCES app_chat.conversation (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- !section
-- section TRIGGER FUNCTIONS
-- anchor set_conversation_type
-- check if conversation member count on insert and delete
-- and if greater than 2, set group column to true
CREATE OR REPLACE FUNCTION app_chat.set_conversation_type () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_member_count INTEGER;
BEGIN
  -- On INSERT
  IF TG_OP = 'INSERT' THEN
    SELECT COUNT(*) INTO v_member_count
    FROM app_chat.member
    WHERE conversation_id = NEW.conversation_id;

    IF v_member_count > 2 THEN
      UPDATE app_chat.conversation
      SET is_group = TRUE
      WHERE id = NEW.conversation_id;
    END IF;
  -- On DELETE
  ELSIF TG_OP = 'DELETE' THEN
    SELECT COUNT(*) INTO v_member_count
    FROM app_chat.member
    WHERE conversation_id = OLD.conversation_id;

    IF v_member_count <= 2 THEN
      UPDATE app_chat.conversation
      SET is_group = FALSE
      WHERE id = OLD.conversation_id;
    END IF;
  END IF;
  RETURN NULL;
END;
$$
;

-- anchor handle_user_block_change
CREATE OR REPLACE FUNCTION app_chat.handle_user_block_change () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_conversation_id UUID;
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Find the conversation between the blocker and the blocked user
    SELECT
      c.id INTO v_conversation_id
    FROM
      app_chat.conversation c
    WHERE
      NOT c.is_group AND EXISTS (
        SELECT
          1
        FROM
          app_chat.member m1
          JOIN app_chat.member m2 ON m1.conversation_id = m2.conversation_id
        WHERE
          c.id = m1.conversation_id
          AND m1.user_id = NEW.blocker_id
          AND m2.user_id = NEW.blocked_id
      );
    -- If a conversation exists, update it and log the block
    IF v_conversation_id IS NOT NULL THEN
      UPDATE
        app_chat.conversation
      SET
        is_blocked = TRUE
      WHERE
        id = v_conversation_id;
      INSERT INTO app_chat.log (conversation_id, user_id, action)
        VALUES (v_conversation_id, NEW.blocker_id, 'user.block');
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    -- When a user unblocks another, set is_blocked to FALSE for conversations between them
    -- Only unblock if there is no other active block in either direction
    UPDATE
      app_chat.conversation c
    SET
      is_blocked = FALSE
    WHERE
      NOT c.is_group AND EXISTS (
        SELECT
          1
        FROM
          app_chat.member m1
          JOIN app_chat.member m2 ON m1.conversation_id = m2.conversation_id
        WHERE
          c.id = m1.conversation_id
          AND m1.user_id = OLD.blocker_id
          AND m2.user_id = OLD.blocked_id
      ) AND NOT EXISTS (
        SELECT
          1
        FROM
          app_account.user_block ub
        WHERE (ub.blocker_id = OLD.blocker_id
          AND ub.blocked_id = OLD.blocked_id)
        OR (ub.blocker_id = OLD.blocked_id
          AND ub.blocked_id = OLD.blocker_id)
      );
  END IF;
  RETURN NULL;
END;
$$
;

-- anchor archive_deleted_message
CREATE OR REPLACE FUNCTION app_chat.archive_deleted_message () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  INSERT INTO app_chat.deleted_message (
    id,
    conversation_id,
    sender_id,
    content,
    created_at,
    updated_at,
    deleted_at
  )
  VALUES (
    OLD.id,
    OLD.conversation_id,
    OLD.sender_id,
    OLD.content,
    OLD.created_at,
    OLD.updated_at,
    NOW()
  );
  RETURN OLD;
END;
$$
;

-- anchor broadcast_message
CREATE OR REPLACE FUNCTION app_chat.broadcast_message () returns trigger security definer language plpgsql AS $$
begin
  perform realtime.send(
    row_to_json(COALESCE(NEW, OLD))::jsonb,
    TG_OP,
    'chat:' || COALESCE(NEW.conversation_id::text, OLD.conversation_id::text),
    true
  );

  return null;
end;
$$
;

-- anchor broadcast_log
CREATE OR REPLACE FUNCTION app_chat.broadcast_log () returns trigger security definer language plpgsql AS $$
begin
  perform realtime.send(
    row_to_json(NEW)::jsonb,
    'LOG',
    'chat:' || NEW.conversation_id::text,
    true
  );

  return null;
end;
$$
;

-- anchor broadcast_message_read
CREATE OR REPLACE FUNCTION app_chat.broadcast_message_read () returns trigger security definer language plpgsql AS $$
DECLARE
  v_conversation_id UUID;
begin
  SELECT conversation_id INTO v_conversation_id FROM app_chat.message WHERE id = NEW.message_id;

  perform realtime.send(
    row_to_json(NEW)::jsonb,
    'READ',
    'chat:' || v_conversation_id::text,
    true
  );

  return null;
end;
$$
;

-- !section
-- section TRIGGERS
-- anchor set_conversation_type
CREATE TRIGGER set_conversation_type
AFTER INSERT
OR DELETE ON app_chat.member FOR EACH ROW
EXECUTE FUNCTION app_chat.set_conversation_type ()
;

-- anchor user_block
CREATE TRIGGER user_block_handle_change
AFTER INSERT
OR DELETE ON app_account.user_block FOR EACH ROW
EXECUTE FUNCTION app_chat.handle_user_block_change ()
;

-- anchor archive_deleted_message
CREATE TRIGGER archive_deleted_message BEFORE DELETE ON app_chat.message FOR EACH ROW
EXECUTE FUNCTION app_chat.archive_deleted_message ()
;

-- anchor broadcast_message
CREATE TRIGGER broadcast_message
AFTER insert
OR
UPDATE
OR delete ON app_chat.message FOR each ROW
EXECUTE function app_chat.broadcast_message ()
;

-- anchor broadcast_log
CREATE TRIGGER broadcast_log
AFTER insert ON app_chat.log FOR each ROW
EXECUTE function app_chat.broadcast_log ()
;

-- anchor broadcast_message_read
CREATE TRIGGER broadcast_message_read
AFTER insert ON app_chat.message_read FOR each ROW
EXECUTE function app_chat.broadcast_message_read ()
;

-- !section
-- section RLS POLICIES
-- anchor conversation
ALTER TABLE app_chat.conversation ENABLE ROW LEVEL SECURITY
;

CREATE POLICY conversation_select_member ON app_chat.conversation FOR
SELECT
  USING (
    app_chat.is_member (id, auth.uid ())
    OR app_access.has_capability ('chat.conversation.all.view')
  )
;

-- anchor member
ALTER TABLE app_chat.member ENABLE ROW LEVEL SECURITY
;

CREATE POLICY member_select_member ON app_chat.member FOR
SELECT
  USING (
    app_chat.is_member (conversation_id, auth.uid ())
  )
;

-- anchor message
ALTER TABLE app_chat.message ENABLE ROW LEVEL SECURITY
;

CREATE POLICY message_select_member ON app_chat.message FOR
SELECT
  USING (
    app_chat.is_member (conversation_id, auth.uid ())
    AND NOT app_chat.is_conversation_blocked (conversation_id)
    OR app_access.has_capability ('chat.message.all.view')
  )
;

CREATE POLICY message_insert_member ON app_chat.message FOR INSERT
WITH
  CHECK (
    app_chat.is_member (conversation_id, auth.uid ())
    AND sender_id = auth.uid ()
    AND NOT app_chat.is_conversation_blocked (conversation_id)
  )
;

CREATE POLICY message_update_member ON app_chat.message
FOR UPDATE
  USING (
    app_chat.is_member (conversation_id, auth.uid ())
    AND sender_id = auth.uid ()
    AND NOT app_chat.is_conversation_blocked (conversation_id)
  )
WITH
  CHECK (
    app_chat.is_member (conversation_id, auth.uid ())
    AND sender_id = auth.uid ()
    AND NOT app_chat.is_conversation_blocked (conversation_id)
  )
;

CREATE POLICY message_delete_member ON app_chat.message FOR DELETE USING (
  app_chat.is_member (conversation_id, auth.uid ())
  AND sender_id = auth.uid ()
)
;

CREATE POLICY message_delete_admin ON app_chat.message FOR DELETE USING (
  app_access.has_capability ('chat.message.all.delete')
)
;

-- anchor deleted_message
ALTER TABLE app_chat.deleted_message ENABLE ROW LEVEL SECURITY
;

CREATE POLICY deleted_message_select_admin ON app_chat.deleted_message FOR
SELECT
  USING (
    app_access.has_capability (
      'chat.deleted_message.all.view'
    )
  )
;

-- anchor conversation_preference
ALTER TABLE app_chat.conversation_preference ENABLE ROW LEVEL SECURITY
;

CREATE POLICY conversation_preference_select ON app_chat.conversation_preference FOR
SELECT
  USING (
    user_id = auth.uid ()
    OR app_access.has_capability (
      'chat.conversation_preference.all.view'
    )
  )
;

CREATE POLICY conversation_preference_insert ON app_chat.conversation_preference FOR INSERT
WITH
  CHECK (user_id = auth.uid ())
;

CREATE POLICY conversation_preference_update ON app_chat.conversation_preference
FOR UPDATE
  USING (user_id = auth.uid ())
WITH
  CHECK (user_id = auth.uid ())
;

CREATE POLICY conversation_preference_delete ON app_chat.conversation_preference FOR delete USING (user_id = auth.uid ())
;

-- anchor message_read
ALTER TABLE app_chat.message_read ENABLE ROW LEVEL SECURITY
;

CREATE POLICY message_read_select_member ON app_chat.message_read FOR
SELECT
  USING (
    app_chat.is_member (
      (
        SELECT
          conversation_id
        FROM
          app_chat.message
        WHERE
          id = message_id
      ),
      auth.uid ()
    )
  )
;

-- anchor log
ALTER TABLE app_chat.log ENABLE ROW LEVEL SECURITY
;

CREATE POLICY log_select_member ON app_chat.log FOR
SELECT
  USING (
    app_chat.is_member (conversation_id, auth.uid ())
  )
;

CREATE POLICY log_select_admin ON app_chat.log FOR
SELECT
  USING (
    app_access.has_capability ('chat.log.all.view')
  )
;

-- anchor realtime.messages
-- Dropping is necessary because of different schema names
DROP POLICY if EXISTS "chat_select_member" ON "realtime"."messages"
;

CREATE POLICY "chat_select_member" ON "realtime"."messages" FOR
SELECT
  TO authenticated USING (
    app_chat.is_member (
      REPLACE(
        realtime.topic (),
        'chat:',
        ''
      )::UUID,
      auth.uid ()
    )
    AND realtime.messages.extension IN ('broadcast', 'presence')
  )
;

-- anchor online_select_member
DROP POLICY IF EXISTS "online_select_member" ON "realtime"."messages"
;

CREATE POLICY "online_select_member" ON "realtime"."messages" FOR
SELECT
  TO authenticated USING (
    realtime.topic () LIKE 'online:%'
    AND realtime.messages.extension IN ('presence')
  )
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'chat.conversation.all.view',
      'chat.message.all.view',
      'chat.message.all.delete',
      'chat.conversation_preference.all.view',
      'chat.deleted_message.all.view',
      'chat.log.all.view',
      'chat.start_conversation'
    ]
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY['chat.start_conversation']::TEXT[]
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY['chat.start_conversation']::TEXT[]
  )
;

-- !section